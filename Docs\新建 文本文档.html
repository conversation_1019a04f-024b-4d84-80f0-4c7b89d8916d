<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        #tanchuang {
            position: fixed;
            top: calc(50% - 150px);
            left: calc(50% - 100px);
            width: 300px;
            height: 200px;
            background-color: #949494;
            border: #566abe;
        }
    </style>
</head>

<body class="juzhong" style="display: flex;width: 100vw;height: 100vh;">
    <button id="btn1" onclick="a(txt1)" style="height: 60px;">警告</button>
    <button id="btn2" onclick="a(txt2)" style="height: 60px;">输入</button>
    <button id="btn3" onclick="a(txt3)" style="height: 60px;">蓝色</button>
    <button id="btn4" onclick="a(txt4)" style="height: 60px;">奖励</button>



    <div id="tanchuang" style="display: none;align-items: center;justify-content: center;">
        这是弹窗
    </div>

    <script>
        let a1 = document.getElementById("btn1")
        let a2 = document.getElementById("btn2")
        let a3 = document.getElementById("btn3")
        let a4 = document.getElementById("btn4")
        let b = document.getElementById("tanchuang")

        let txt1 = `<div style="width: 100%;height: 100%;background-color: #ff0000;">这是一个警告框</div>`
        let txt2 = `
                <div style="width: 100%;height: 100%;background-color: #ff0000;">这是一个输入框</div>
                <input type="text">
                `
        let txt3 = `<div style="width: 100%;height: 100%;background-color: blue;">这是一个蓝色弹窗</div>`
        let txt4 = `<div style="width: 100%;height: 100%;background-color: skyblue;">恭喜你获得奖励</div>`

        function c(ttt) {
            return ttt
        }

        // 弹窗逻辑
        const a = (txt) => {
            b.style.display = b.style.display == "flex" ? "none" : "flex"
            if (b.style.display == "flex") {
                b.innerHTML = c(txt)
            } else {
                b.innerHTML = "这是弹窗"
            }
        }
    </script>
</body>

</html>