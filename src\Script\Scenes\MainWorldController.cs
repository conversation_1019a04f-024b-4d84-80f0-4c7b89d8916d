/*
 * MainWorld场景控制器
 * 负责确保MainWorld场景正确初始化
 */

using Godot;
using ArchipelagoGame.Manager;

namespace ArchipelagoGame.Scenes
{
    /// <summary>
    /// MainWorld场景控制器
    /// 确保场景完全加载后初始化相关系统
    /// </summary>
    public partial class MainWorldController : Node2D
    {
        #region Godot生命周期

        /// <summary>
        /// 场景准备就绪时调用
        /// </summary>
        public override void _Ready()
        {
            // 延迟初始化，确保所有子节点都已加载
            // CallDeferred(MethodName.InitializeScene);
            // C#: 通过绝对路径获取节点（从场景根节点开始）
            Node2D Subterranean = GetNode<Node2D>("/root/MainWorld/Subterranean");
            Node2D GroundSurface = GetNode<Node2D>("/root/MainWorld/GroundSurface");
            Node2D Atmosphere = GetNode<Node2D>("/root/MainWorld/Atmosphere");
            Subterranean.Visible = false;
            Atmosphere.Visible = false;
            GD.Print("MainWorldController: Scene is ready, initializing systems...", Subterranean);
        }

        #endregion

        #region 私有方法

        // /// <summary>
        // /// 初始化场景,(这里后续使用种子生成的时候再使用初始化地图)
        // /// </summary>
        // private void InitializeScene()
        // {
            
        // }

        #endregion
    }
}
