/*
 * 玩家状态机 - 状态管理和动画控制
 */

using Godot;

namespace ArchipelagoGame.Manager
{
    /// <summary>
    /// 玩家状态机 - 负责状态管理(idle/walk/attack)和动画控制
    /// 继承自Node以作为游戏对象的组件
    /// </summary>
    public partial class PlayerStateMachine : Node
    {
        #region 信号定义

        /// <summary>状态改变信号</summary>
        [Signal]
        public delegate void StateChangedEventHandler(string newState);

        #endregion

        #region 私有字段

        /// <summary>动画播放器引用</summary>
        private AnimationPlayer _animationPlayer;

        /// <summary>角色精灵引用</summary>
        private Sprite2D _playerSprite;

        /// <summary>当前动画名称</summary>
        private string _currentAnimation = "";

        /// <summary>角色是否面向左侧</summary>
        private bool _facingLeft = false;

        /// <summary>是否正在播放攻击动画</summary>
        private bool _isAttacking = false;

        /// <summary>上一帧鼠标左键是否被按下</summary>
        private bool _wasMousePressed = false;

        #endregion

        #region Godot生命周期

        /// <summary>
        /// 节点准备就绪时初始化
        /// </summary>
        public override void _Ready()
        {
            // 获取父节点的子节点引用
            var parent = GetParent();
            _animationPlayer = parent.GetNode<AnimationPlayer>("AnimationPlayer");
            _playerSprite = parent.GetNode<Sprite2D>("PlayerSprite");

            // 连接动画完成信号
            if (_animationPlayer != null)
            {
                _animationPlayer.AnimationFinished += OnAnimationFinished;
            }

            // 播放默认idle动画
            PlayAnimation("idle");
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新角色动画状态
        /// </summary>
        /// <param name="inputDirection">输入方向</param>
        public void UpdateAnimation(Vector2 inputDirection)
        {
            // 如果正在攻击，忽略所有其他动画请求
            if (_isAttacking)
            {
                return;
            }

            // 检测鼠标左键单次点击 
            bool mousePressed = Input.IsMouseButtonPressed(MouseButton.Left);
            bool mouseJustPressed = mousePressed && !_wasMousePressed;
            _wasMousePressed = mousePressed;

            // 检测攻击输入 
            if (mouseJustPressed)
            {
                StartAttackAnimation();
                return; // 攻击动画优先，直接返回
            }

            // 根据移动状态选择动画
            string targetAnimation = inputDirection != Vector2.Zero ? "walk" : "idle";

            // 更新角色朝向
            if (inputDirection != Vector2.Zero)
            {
                UpdateFacing();
            }

            // 播放目标动画
            PlayAnimation(targetAnimation);
        }

        /// <summary>
        /// 播放攻击动画 - 公共接口
        /// </summary>
        public void PlayAttackAnimation()
        {
            StartAttackAnimation();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 播放指定动画
        /// </summary>
        /// <param name="animationName">动画名称</param>
        private void PlayAnimation(string animationName)
        {
            if (_currentAnimation != animationName && _animationPlayer != null)
            {
                _currentAnimation = animationName;
                _animationPlayer.Play(animationName);
                EmitSignal(SignalName.StateChanged, animationName);
            }
        }

        /// <summary>
        /// 根据输入方向更新角色朝向 - 基于WASD键的方向映射
        /// </summary>
        private void UpdateFacing()
        {
            if (_playerSprite == null) return;

            // 基于WASD键的方向映射逻辑：
            // W键（向上移动）：角色朝向右侧（FlipH = false）
            // S键（向下移动）：角色朝向左侧（FlipH = true）
            // A键和D键：保持原有的左右移动逻辑

            // 检测具体按键输入来决定朝向
            bool wPressed = Input.IsActionPressed("ui_up") || Input.IsKeyPressed(Key.W);
            bool sPressed = Input.IsActionPressed("ui_down") || Input.IsKeyPressed(Key.S);
            bool aPressed = Input.IsActionPressed("ui_left") || Input.IsKeyPressed(Key.A);
            bool dPressed = Input.IsActionPressed("ui_right") || Input.IsKeyPressed(Key.D);

            // 优先级：左右移动 > 上下移动
            if (dPressed && !aPressed) // 纯右移动或右移动占主导
            {
                if (_facingLeft)
                {
                    _facingLeft = false;
                    _playerSprite.FlipH = false;
                }
            }
            else if (aPressed && !dPressed) // 纯左移动或左移动占主导
            {
                if (!_facingLeft)
                {
                    _facingLeft = true;
                    _playerSprite.FlipH = true;
                }
            }
            else if (wPressed && !sPressed && !aPressed && !dPressed) // 纯上移动
            {
                if (_facingLeft)
                {
                    _facingLeft = false;
                    _playerSprite.FlipH = false; // W键：朝向右侧
                }
            }
            else if (sPressed && !wPressed && !aPressed && !dPressed) // 纯下移动
            {
                if (!_facingLeft)
                {
                    _facingLeft = true;
                    _playerSprite.FlipH = true; // S键：朝向左侧
                }
            }
            // 对角线移动时，水平方向优先级更高，已在上面的A/D判断中处理
        }

        /// <summary>
        /// 开始攻击动画 - 设置攻击状态并播放动画
        /// </summary>
        private void StartAttackAnimation()
        {
            if (_isAttacking) return; // 如果已在攻击，忽略新的攻击请求

            _isAttacking = true;
            PlayAnimation("attack");
        }

        /// <summary>
        /// 动画完成时的回调处理
        /// </summary>
        /// <param name="animationName">完成的动画名称</param>
        private void OnAnimationFinished(StringName animationName)
        {
            // 如果攻击动画完成，重置攻击状态
            if (animationName == "attack" && _isAttacking)
            {
                _isAttacking = false;
                // 动画完成后，下一帧会自动根据当前输入状态选择合适的动画
            }
        }

        #endregion
    }
}

// GD0102: The type of the exported member '' is not supported (https://docs.godotengine.org/en/4.4/tutorials/scripting/c_sharp/diagnostics/GD0102.html) D:\MyCode\My_Project\ArchipelagoGame\archipelago-game\src\Script\Player\PlayerController.cs(20,27)
