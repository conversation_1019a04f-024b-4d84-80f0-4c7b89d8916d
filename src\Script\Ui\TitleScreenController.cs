/*
 * 标题屏幕控制器 - 游戏主菜单界面控制器
 * 作用：管理主菜单的按钮交互和场景跳转
 * 继承：BaseUIPanel -> TitleScreenController
 * 场景结构：需要包含V(VBoxContainer)和四个按钮节点
 */

using Godot;
using ArchipelagoGame.Themes;

namespace ArchipelagoGame.UI
{
    /// <summary>
    /// 标题屏幕控制器 - 管理游戏主菜单界面
    /// 提供单人游戏、多人游戏、设置和退出功能
    /// </summary>
    public partial class TitleScreenController : BaseUIPanel
    {
        /// <summary>面板标识名称</summary>
        public override string PanelName => "TitleScreen";

        // UI按钮引用
        private Button _singlePlayerButton;  // 单人游戏按钮
        private Button _multiplayerButton;   // 多人游戏按钮
        private Button _settingsButton;      // 设置按钮
        private Button _exitButton;          // 退出游戏按钮
        private VBoxContainer _vBox;         // 按钮容器
        private Label _gameNameLabel;        // 游戏名称标签

        /// <summary>
        /// 初始化面板 - 获取UI元素并绑定事件
        /// </summary>
        public override void Initialize()
        {
            // 获取UI节点引用
            _vBox = GetNode<VBoxContainer>("V");
            _gameNameLabel = ThemeApplicator.GetLabelSafely(this, "GameName");

            // 获取各按钮节点引用
            _singlePlayerButton = _vBox.GetNode<Button>("Single_player_game");
            _multiplayerButton = _vBox.GetNode<Button>("multiplayer");
            _settingsButton = _vBox.GetNode<Button>("set_up");
            _exitButton = _vBox.GetNode<Button>("Exit the game");

            // 连接按钮点击信号
            _singlePlayerButton.Pressed += OnSinglePlayerPressed;
            _multiplayerButton.Pressed += OnMultiplayerPressed;
            _settingsButton.Pressed += OnSettingsPressed;
            _exitButton.Pressed += OnExitPressed;

            // 应用主题
            ApplyThemes();
        }

        /// <summary>
        /// 应用主题到UI元素
        /// </summary>
        private void ApplyThemes()
        {
            // 确保ThemeManager已初始化基础主题
            // ThemeManager.Instance?.CreateBaseThemes();

            // 应用游戏名称标签主题（如果节点存在）
            if (_gameNameLabel != null)
            {
                ThemeApplicator.ApplyThemeToControl(_gameNameLabel, "TitleTheme");
            }

            // 应用VBox容器主题（使用30像素间距）
            ThemeApplicator.ApplyThemeToControl(_vBox, "TitleContainerTheme");
 
            // 为每个按钮应用主题
            ThemeApplicator.ApplyButtonTheme(_singlePlayerButton);
            ThemeApplicator.ApplyButtonTheme(_multiplayerButton);
            ThemeApplicator.ApplyButtonTheme(_settingsButton);
            ThemeApplicator.ApplyButtonTheme(_exitButton);
        }

        /// <summary>单人游戏按钮点击 - 跳转到主游戏场景</summary>
        private void OnSinglePlayerPressed()
        {
            // UIManager.Instance?.SwitchToScene("res://src/Scenes/GameViwes/MainWorld.tscn");
        }

        /// <summary>多人游戏按钮点击 - 功能待实现</summary>
        private void OnMultiplayerPressed()
        {
            // TODO: 实现多人游戏功能
        }

        /// <summary>设置按钮点击 - 跳转到设置界面</summary>
        private void OnSettingsPressed()
        {
            // UIManager.Instance?.SwitchToScene("res://src/Scenes/SettingsViews/GameSettings.tscn");
        }

        /// <summary>退出游戏按钮点击 - 直接退出应用程序</summary>
        private void OnExitPressed()
        {
            GetTree().Quit();
        }


    }
}