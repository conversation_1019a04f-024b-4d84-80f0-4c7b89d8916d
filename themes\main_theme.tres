[gd_resource type="Theme" load_steps=4 format=3 uid="uid://cymuci7wp8fac"]

[ext_resource type="FontFile" uid="uid://bqgr6fg4e623n" path="res://Assets/font/汇文仿宋v1.001.ttf" id="1_3nr6q"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5agko"]
content_margin_left = 10.0
content_margin_top = 15.0
content_margin_right = 10.0
content_margin_bottom = 29.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5agko"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0, 0, 0, 0.6)
corner_radius_top_left = 3
corner_radius_top_right = 3
corner_radius_bottom_right = 3
corner_radius_bottom_left = 3
corner_detail = 5

[resource]
default_font = ExtResource("1_3nr6q")
default_font_size = 30
Button/colors/font_color = Color(0.276446, 0.331307, 0.426834, 1)
Button/colors/font_disabled_color = Color(0.875, 0.875, 0.875, 0.5)
Button/colors/font_focus_color = Color(0.95, 0.95, 0.95, 1)
Button/colors/font_hover_color = Color(0.95, 0.95, 0.95, 1)
Button/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
Button/colors/font_outline_color = Color(0, 0, 0, 1)
Button/colors/font_pressed_color = Color(1, 1, 1, 1)
Button/colors/icon_disabled_color = Color(1, 1, 1, 0.4)
Button/colors/icon_focus_color = Color(1, 1, 1, 1)
Button/colors/icon_hover_color = Color(1, 1, 1, 1)
Button/colors/icon_hover_pressed_color = Color(1, 1, 1, 1)
Button/colors/icon_normal_color = Color(1, 1, 1, 1)
Button/colors/icon_pressed_color = Color(1, 1, 1, 1)
Button/constants/align_to_largest_stylebox = 0
Button/constants/h_separation = 4
Button/constants/icon_max_width = 0
Button/constants/outline_size = 0
Button/styles/disabled = SubResource("StyleBoxEmpty_5agko")
Button/styles/focus = SubResource("StyleBoxEmpty_5agko")
Button/styles/hover = SubResource("StyleBoxEmpty_5agko")
Button/styles/normal = SubResource("StyleBoxEmpty_5agko")
Button/styles/pressed = SubResource("StyleBoxFlat_5agko")
VBoxContainer/constants/separation = 4
