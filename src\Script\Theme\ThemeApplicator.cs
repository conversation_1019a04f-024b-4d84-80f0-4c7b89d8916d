/*
 * 主题应用器 - 统一的主题应用工具类
 * 作用：提供统一的主题应用方法，消除重复代码
 * 用途：为UI控制器提供标准化的主题应用功能
 */

using Godot;
using ArchipelagoGame.Themes;

namespace ArchipelagoGame.UI
{
    /// <summary>
    /// 主题应用器 - 提供统一的主题应用方法
    /// </summary>
    public static class ThemeApplicator
    {
        /// <summary>
        /// 为按钮应用完整主题（包括透明背景设置）
        /// </summary>
        /// <param name="button">目标按钮</param>
        public static void ApplyButtonTheme(Button button)
        {
            if (button == null) return;

            // 应用基础按钮主题
            ApplyThemeToControl(button, "DefaultFontTheme", "DefaultColorTheme", "DefaultStyleTheme");

            // 设置焦点模式为None（移除焦点边框）
            button.FocusMode = Control.FocusModeEnum.None;
        }

        /// <summary>
        /// 应用主题到控件
        /// </summary>
        /// <param name="control">目标控件</param>
        /// <param name="themeNames">主题名称列表</param>
        public static void ApplyThemeToControl(Control control, params string[] themeNames)
        {
            if (control == null) return;

            foreach (var themeName in themeNames)
            {
                // ThemeManager.Instance?.ApplyTheme(control, themeName);
            }
        }



        /// <summary>
        /// 安全地获取节点
        /// </summary>
        /// <typeparam name="T">节点类型</typeparam>
        /// <param name="parent">父节点</param>
        /// <param name="nodePath">节点路径</param>
        /// <returns>节点实例，如果不存在则返回null</returns>
        public static T GetNodeSafely<T>(Node parent, string nodePath) where T : Node
        {
            if (parent == null || string.IsNullOrEmpty(nodePath)) return null;

            try
            {
                if (parent.HasNode(nodePath))
                {
                    return parent.GetNode<T>(nodePath);
                }
            }
            catch (System.Exception ex)
            {
                GD.PrintErr($"[ThemeApplicator] 获取节点失败 '{nodePath}': {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 安全地获取Label节点（支持多种可能的名称）
        /// </summary>
        /// <param name="parent">父节点</param>
        /// <param name="possibleNames">可能的节点名称列表</param>
        /// <returns>Label节点实例，如果不存在则返回null</returns>
        public static Label GetLabelSafely(Node parent, params string[] possibleNames)
        {
            if (parent == null) return null;
            foreach (var name in possibleNames)
            {
                var label = GetNodeSafely<Label>(parent, name);
                if (label != null)
                {
                    return label;
                }
            }

            return null;
        }
    }
}
