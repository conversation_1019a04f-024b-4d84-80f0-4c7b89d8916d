/*
 * 洞穴出口 - 返回主世界的出口点
 */

using Godot;
using ArchipelagoGame.Themes;

namespace ArchipelagoGame.Interaction
{
    /// <summary>
    /// 洞穴出口 - 处理从洞穴返回主世界的逻辑
    /// 继承自Area2D以获得区域检测功能
    /// </summary>
    public partial class CaveExit : Area2D
    {
        #region 导出属性

        /// <summary>出口名称</summary>
        [Export]
        public string ExitName { get; set; } = "洞穴";

        /// <summary>是否显示交互提示</summary>
        [Export]
        public bool ShowInteractionPrompt { get; set; } = true;

        #endregion
        
        #region 私有字段
        
        /// <summary>出口精灵</summary>
        private Sprite2D _exitSprite;
        
        /// <summary>交互提示标签</summary>
        private Label _promptLabel;
        
        /// <summary>碰撞形状</summary>
        private CollisionShape2D _collisionShape;
        
        /// <summary>是否鼠标悬停</summary>
        private bool _isMouseOver = false;
        
        /// <summary>原始颜色</summary>
        private Color _originalColor = Colors.White;
        
        #endregion
        
        #region Godot生命周期
        
        public override void _Ready()
        {
            // 设置为可交互组
            AddToGroup("interactable");

            // 获取手动创建的子节点引用
            InitializeChildNodes();

            // 连接信号
            InputEvent += OnInputEvent;
            MouseEntered += OnMouseEntered;
            MouseExited += OnMouseExited;

            // 初始化交互提示
            if (_promptLabel != null)
            {
                _promptLabel.Visible = false;
                UpdatePromptText();
            }

            GD.Print($"洞穴出口已初始化: {ExitName}");
        }
        
        #endregion
        
        #region 信号处理
        
        /// <summary>
        /// 处理输入事件（鼠标点击）
        /// </summary>
        private void OnInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent)
            {
                if (mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Left)
                {
                    ExitCave();
                }
            }
        }
        
        /// <summary>
        /// 鼠标进入区域
        /// </summary>
        private void OnMouseEntered()
        {
            _isMouseOver = true;
            
            // 高亮显示出口
            if (_exitSprite != null)
            {
                _exitSprite.Modulate = new Color(1.2f, 1.2f, 1.2f);
            }
            
            // 显示交互提示
            if (_promptLabel != null && ShowInteractionPrompt)
            {
                _promptLabel.Visible = true;
            }
            
            GD.Print($"鼠标进入洞穴出口: {ExitName}");
        }
        
        /// <summary>
        /// 鼠标离开区域
        /// </summary>
        private void OnMouseExited()
        {
            _isMouseOver = false;
            
            // 恢复正常显示
            if (_exitSprite != null)
            {
                _exitSprite.Modulate = _originalColor;
            }
            
            // 隐藏交互提示
            if (_promptLabel != null)
            {
                _promptLabel.Visible = false;
            }
        }
        
        #endregion
        
        #region 私有方法

        /// <summary>
        /// 初始化子节点引用并应用主题
        /// </summary>
        private void InitializeChildNodes()
        {
            // 获取手动创建的子节点引用
            _collisionShape = GetNodeOrNull<CollisionShape2D>("ExitCollision");
            _exitSprite = GetNodeOrNull<Sprite2D>("ExitVisual");
            _promptLabel = GetNodeOrNull<Label>("ExitPromptLabel");

            // 验证必要节点是否存在并给出友好提示
            if (_collisionShape == null)
            {
                GD.PrintErr($"洞穴出口 {Name}: 未找到 ExitCollision 子节点，请在编辑器中手动添加");
            }

            if (_exitSprite == null)
            {
                GD.PrintErr($"洞穴出口 {Name}: 未找到 ExitVisual 子节点，请在编辑器中手动添加");
            }
            else
            {
                // 记录原始颜色
                _originalColor = _exitSprite.Modulate;
            }

            if (_promptLabel == null && ShowInteractionPrompt)
            {
                GD.PrintErr($"洞穴出口 {Name}: 未找到 ExitPromptLabel 子节点，交互提示将不可用");
            }
            else if (_promptLabel != null)
            {
                // 应用主题到交互提示标签
                ApplyThemeToPromptLabel();
            }
        }

        /// <summary>
        /// 为交互提示标签应用主题
        /// </summary>
        private void ApplyThemeToPromptLabel()
        {
            if (_promptLabel == null) return;

            // 确保ThemeManager已初始化
            // ThemeManager.Instance?.CreateBaseThemes();

            // // 应用字体和颜色主题
            // ThemeManager.Instance?.ApplyTheme(_promptLabel, "DefaultFontTheme");
            // ThemeManager.Instance?.ApplyTheme(_promptLabel, "DefaultColorTheme");

            GD.Print($"洞穴出口 {Name}: 已应用主题到交互提示标签");
        }
        

        
        /// <summary>
        /// 离开洞穴 - 使用智能记忆系统自动管理返回位置
        /// </summary>
        private void ExitCave()
        {
            GD.Print($"玩家离开洞穴: {ExitName} -> 主世界");

            // 通过交互管理器返回主世界（自动使用记忆位置）
            // if (InteractionManager.Instance != null)
            // {
            //     InteractionManager.Instance.ReturnToMainWorld();
            // }
            // else
            // {
            //     GD.PrintErr("InteractionManager 实例不存在！请检查 project.godot 中的 autoload 配置");
            // }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置出口纹理（运行时调用）
        /// </summary>
        public void SetExitTexture(Texture2D texture)
        {
            if (_exitSprite != null)
            {
                _exitSprite.Texture = texture;
            }
        }
        
        /// <summary>
        /// 设置碰撞区域大小（运行时调用）
        /// </summary>
        public void SetCollisionSize(Vector2 size)
        {
            if (_collisionShape?.Shape is RectangleShape2D rectShape)
            {
                rectShape.Size = size;
            }
        }

        /// <summary>
        /// 更新交互提示文本 - 根据ExitName动态生成
        /// </summary>
        public void UpdatePromptText()
        {
            if (_promptLabel != null)
            {
                // 当ExitName为空或默认值时保持简洁文本，否则使用个性化文本
                if (string.IsNullOrEmpty(ExitName))
                {
                    _promptLabel.Text = "返回主世界";
                }
                else
                {
                    _promptLabel.Text = $"离开 {ExitName}";
                }
            }
        }
        
        #endregion
    }
}
