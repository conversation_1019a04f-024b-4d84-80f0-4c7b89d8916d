using Godot;

namespace ArchipelagoGame.VoidBackground
{
    /// <summary>
    /// 虚空背景控制器
    /// 控制虚空背景的雾气效果，包括流动、密度和颜色变化
    /// </summary>
    public partial class VoidBackgroundController : ColorRect
    {
        #region 导出参数
        
        [Export] // 设置烟雾流动速度
        public Vector2 velocity { get; set; }
        public Vector2 defult_velocity { get; set; } = new Vector2(3f, 0.0f);

        [Export] // 控制噪声缩放
        public float noise_scale { get; set; }

        private float _fogDensity = 1.0f;

        [Export] // 控制噪声密度
        public float fog_density
        {
            get => _fogDensity;
            set
            {
                _fogDensity = value;
                // 如果已初始化且禁用了键盘控制，则应用新的迷雾密度
                if (_shaderMaterial != null && !_enableKeyControlFog)
                {
                    _shaderMaterial.SetShaderParameter("fog_density", _fogDensity);
                }
            }
        }

        // Z轴映射迷雾的参数设置
        [Export(PropertyHint.Range, "0, 1000, 10")] // Z轴最小值，0表示地面高度
        public float MinZHeight { get; set; } = 0f;

        [Export(PropertyHint.Range, "100, 1000, 10")] // Z轴最大值，达到此高度时迷雾达到最大浓度
        public float MaxZHeight { get; set; } = 600f;

        // fog_density的取值范围
        [Export(PropertyHint.Range, "0.1, 3.0, 0.1")] // 迷雾最浓时的density值（Z=MaxZHeight）
        public float MinFogDensity { get; set; } = 1.4f;

        [Export(PropertyHint.Range, "1.0, 10.0, 0.1")] // 迷雾不可见时的density值（Z=MinZHeight）
        public float MaxFogDensity { get; set; } = 3.5f;

        [Export] // 控制迷雾颜色
        public Vector4 fog_color { get; set; } = new Vector4(0.719f, 0.392f, 1.0f, 0.0f);

        // 是否启用SHIFT/CTRL键控制迷雾浓度
        private bool _enableKeyControlFog = false;

        [Export]
        public bool EnableKeyControlFog
        {
            get => _enableKeyControlFog;
            set
            {
                _enableKeyControlFog = value;
                // 如果已初始化则应用相应的迷雾设置
                if (_shaderMaterial != null)
                {
                    ApplyFogDensity();
                }
            }
        }

        #endregion

        #region 私有字段

        // 当前ShaderMaterial实例
        private ShaderMaterial _shaderMaterial;

        // 迷雾流动的距离
        private Vector2 current_offset = new Vector2(0.0f, 0.0f);

        #endregion

        #region 生命周期

        // 节点进入场景树时调用
        public override void _Ready()
        {
            SetAnchorsPreset(LayoutPreset.FullRect); // 设置节点锚点，使其覆盖全屏
            velocity = defult_velocity;

            // 保存ShaderMaterial实例
            _shaderMaterial = Material as ShaderMaterial;

            // 应用初始设置
            ApplyFogDensity();
            
            GD.Print("VoidBackgroundController 初始化完成");
        }

        public override void _PhysicsProcess(double delta)
        {
            if (_shaderMaterial != null)
            {
                // 计算位移时乘以delta时间
                Vector2 displacement = PixelToUV(velocity);
                // 累加位移
                current_offset += displacement;
                // 更新着色器中的offset参数
                _shaderMaterial.SetShaderParameter("offset", current_offset);
                velocity = defult_velocity;
            }
        }

        #endregion

        #region 私有方法

        // 应用迷雾密度到着色器
        private void ApplyFogDensity()
        {
            if (_shaderMaterial != null)
            {
                if (_enableKeyControlFog)
                {
                    // 启用键盘控制时，初始设置为最淡的迷雾（Z=0时的映射值）
                    _shaderMaterial.SetShaderParameter("fog_density", MaxFogDensity);
                }
                else
                {
                    // 禁用键盘控制时，使用Inspector中设置的fog_density值
                    _shaderMaterial.SetShaderParameter("fog_density", _fogDensity);
                }
            }
        }

        // 内嵌的工具方法：像素到UV坐标转换
        private Vector2 PixelToUV(Vector2 pixelVelocity)
        {
            Vector2 resolution = GetViewportRect().Size;
            return pixelVelocity / resolution;
        }

        // 根据Z轴值更新迷雾浓度
        private void UpdateFogDensity(float zValue)
        {
            // 如果未启用键盘控制迷雾，则直接返回
            if (!_enableKeyControlFog)
                return;

            if (_shaderMaterial != null)
            {
                // 映射Z轴值到迷雾浓度 (MaxFogDensity 到 MinFogDensity)
                // 当z=MinZHeight时fog_density=MaxFogDensity，当z>=MaxZHeight时fog_density=MinFogDensity
                float mappedDensity;
                if (zValue <= MinZHeight)
                {
                    mappedDensity = MaxFogDensity; // Z=MinZHeight时迷雾最淡
                }
                else if (zValue >= MaxZHeight)
                {
                    mappedDensity = MinFogDensity; // Z=MaxZHeight时迷雾最浓
                }
                else
                {
                    // 线性映射，z值在MinZHeight到MaxZHeight之间反向映射到MaxFogDensity到MinFogDensity
                    float t = (zValue - MinZHeight) / (MaxZHeight - MinZHeight); // 归一化进度: 0到1
                    mappedDensity = MaxFogDensity - t * (MaxFogDensity - MinFogDensity);
                }

                // 更新着色器中的fog_density参数
                _shaderMaterial.SetShaderParameter("fog_density", mappedDensity);
            }
        }

        #endregion

        #region 公共接口

        /// <summary>
        /// 更新玩家速度，影响雾气流动
        /// </summary>
        /// <param name="diff_velocity">速度差值</param>
        public void UpdateVelocity(Vector2 diff_velocity)
        {
            if (_shaderMaterial != null)
            {
                // 计算相对速度
                velocity += diff_velocity / 10;
            }
        }

        /// <summary>
        /// 根据Z轴值更新迷雾浓度（公共接口）
        /// </summary>
        /// <param name="zValue">Z轴值</param>
        public void UpdateZHeight(float zValue)
        {
            UpdateFogDensity(zValue);
        }

        #endregion
    }
}
