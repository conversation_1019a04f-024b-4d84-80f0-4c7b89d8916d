shader_type canvas_item;

// 着色器参数
uniform float noise_scale = 3.0;
uniform float fog_density = 1.0; // 默认雾气密度
uniform vec4 fog_color : source_color = vec4(0.719, 0.392, 1.0, 1.0);
uniform vec2 offset = vec2(0.0, 0.0); // 累计位移变量，由脚本控制

// 保持原有的噪声函数不变
vec2 random(vec2 uv) {
    uv = vec2(dot(uv, vec2(127.1, 311.7)), dot(uv, vec2(269.5, 183.3)));
    return -1.0 + 2.0 * fract(sin(uv) * 43758.5453123);
}

float noise(vec2 uv) {
    vec2 uv_index = floor(uv);
    vec2 uv_fract = fract(uv);
    vec2 blur = smoothstep(0.0, 1.0, uv_fract);

    float a = dot(random(uv_index + vec2(0.0, 0.0)), uv_fract - vec2(0.0, 0.0));
    float b = dot(random(uv_index + vec2(1.0, 0.0)), uv_fract - vec2(1.0, 0.0));
    float c = dot(random(uv_index + vec2(0.0, 1.0)), uv_fract - vec2(0.0, 1.0));
    float d = dot(random(uv_index + vec2(1.0, 1.0)), uv_fract - vec2(1.0, 1.0));

    return mix(mix(a, b, blur.x), mix(c, d, blur.x), blur.y) + 0.5;
}

float fbm(vec2 uv) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;

    // 增加噪波的叠加次数来创造更细腻的云层效果
    for(int i = 0; i < 6; i++) {
        value += amplitude * noise(frequency * uv);
        frequency *= 2.0;
        amplitude *= 0.5;
    }
    return value;
}

void fragment() {
    // 使用累计位移offset移动UV坐标
    vec2 scrolled_uv = UV + offset;

    // 计算噪声值
    float noise_value = fbm(scrolled_uv * noise_scale);

    // 调整fog_amount计算方式，确保即使在低密度下也有明显的云纹理
    float fog_amount = clamp(noise_value * fog_density, 0.0, 1.0);

    // 设置云的颜色和背景色的混合
    vec4 base_color = vec4(0.1, 0.1, 0.2, 1.0);  // 深蓝色背景
    vec4 cloud_color = fog_color;

    // 云的边缘应该更透明，创造柔和的混合效果
    COLOR = mix(base_color, cloud_color, smoothstep(0.1, 0.8, fog_amount));
}
