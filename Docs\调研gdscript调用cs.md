C# 脚本需继承自 Godot 节点类（如 Node、Sprite2D），并标记为 partial 类以支持代码生成

C# 方法暴露给 GDScript​​
​​(1) 定义可调用的方法​​
在 C# 脚本中，方法需满足以下条件：

​​使用 [Export] 或公共方法​​：GDScript 只能调用 public 方法或标记为 [Export] 的字段/属性。
​​参数与返回值类型兼容​​：支持基础类型（int、string、Vector2 等）或 Godot 内置类型（如 NodePath）。
// MyCSharpScript.cs
using Godot;

public partial class MyCSharpNode : Node
{
    // 公共方法（可直接调用）
    public void PrintMessage(string message)
    {
        GD.Print("C# 收到消息: " + message);
    }

    // 带返回值的方法
    public int AddNumbers(int a, int b)
    {
        return a + b;
    }

    // 导出属性（GDScript 可访问）
    [Export]
    public float Speed { get; set; } = 10.0f;
}

GDScript 调用 C# 方法​​
​​(1) 直接调用公共方法​​
# GDScript 代码
extends Node

var csharp_node

func _ready():
    # 获取 C# 节点实例
    csharp_node = get_node("MyCSharpNode")
    # 调用无返回值方法
    csharp_node.PrintMessage("Hello from GDScript!")
    # 调用带返回值方法
    var result = csharp_node.AddNumbers(3, 5)
    print("计算结果: ", result)  # 输出 8
    # 访问导出属性
    csharp_node.Speed = 20.0
​​(2) 连接信号​​
func _ready():
    csharp_node.connect("my_signal", self, "_on_csharp_signal")

func _on_csharp_signal(data):
    print("收到C#信号: ", data)