[gd_scene load_steps=2 format=3 uid="uid://dltarhm6uhhpx"]

[ext_resource type="Script" uid="uid://bw25gx7xwaxe3" path="res://src/Script/Ui/SettingsController.cs" id="1_0pvr0"]

[node name="Settings" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0pvr0")

[node name="SettingsContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -395.0
offset_top = -132.0
offset_right = 390.0
offset_bottom = 244.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="SettingsContainer"]
layout_mode = 2
text = "游戏设置"
horizontal_alignment = 1

[node name="VolumeContainer" type="VBoxContainer" parent="SettingsContainer"]
layout_mode = 2

[node name="MasterVolumeContainer" type="HBoxContainer" parent="SettingsContainer/VolumeContainer"]
layout_mode = 2

[node name="MasterVolumeLabel" type="Label" parent="SettingsContainer/VolumeContainer/MasterVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "主音量: 100%"

[node name="MasterVolumeSlider" type="HSlider" parent="SettingsContainer/VolumeContainer/MasterVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0

[node name="MusicVolumeContainer" type="HBoxContainer" parent="SettingsContainer/VolumeContainer"]
layout_mode = 2

[node name="MusicVolumeLabel" type="Label" parent="SettingsContainer/VolumeContainer/MusicVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "音乐音量: 80%"

[node name="MusicVolumeSlider" type="HSlider" parent="SettingsContainer/VolumeContainer/MusicVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 80.0

[node name="SfxVolumeContainer" type="HBoxContainer" parent="SettingsContainer/VolumeContainer"]
layout_mode = 2

[node name="SfxVolumeLabel" type="Label" parent="SettingsContainer/VolumeContainer/SfxVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "音效音量: 90%"

[node name="SfxVolumeSlider" type="HSlider" parent="SettingsContainer/VolumeContainer/SfxVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 90.0

[node name="BackButton" type="Button" parent="SettingsContainer"]
layout_mode = 2
text = "返回"
